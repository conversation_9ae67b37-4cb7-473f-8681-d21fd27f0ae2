"use client"

import { cn } from "@/lib/utils"
import { GridMotion } from "@/components/ui/grid-motion"
import { SAMPLE_IMAGES, SlideAnimationConfig } from "@/constants/landing/images"
import { useState, useEffect, useCallback } from "react"

interface ScatteredImagesProps {
  className?: string
  animationConfig?: SlideAnimationConfig
}

function shuffleArray<T>(array: T[]): T[] {
  const newArray = [...array]
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]]
  }
  return newArray
}

export function ScatteredImages({ className, animationConfig }: ScatteredImagesProps) {
  const [items, setItems] = useState<any[]>([])
  const [isHovering, setIsHovering] = useState(false)
  const [usedImages, setUsedImages] = useState<Set<string>>(new Set())

  const generateItems = useCallback((config?: SlideAnimationConfig) => {
    // 使用传入的配置或默认配置
    const currentConfig = config || animationConfig
    const imagesPerRow = currentConfig?.imageDisplay?.imagesPerRow || 15
    const ensureAllShown = currentConfig?.imageDisplay?.ensureAllImagesShown || false

    // Convert sample images to the correct format (过滤掉注释的图片)
    const formattedImages = SAMPLE_IMAGES.map(image => ({
      type: 'image' as const,
      src: image.src,
      alt: image.alt,
      width: image.width,
      height: image.height
    }))

    // 动态适配：如果图片数量很少，调整每行图片数量
    const totalAvailableImages = formattedImages.length
    const minImagesPerRow = Math.min(imagesPerRow, Math.ceil(totalAvailableImages / 3))
    const actualImagesPerRow = Math.max(minImagesPerRow, 3) // 至少每行3张
    const totalImages = 3 * actualImagesPerRow



    let availableImages = [...formattedImages]

    // 如果启用了确保所有图片都被展示的功能
    if (ensureAllShown && totalAvailableImages > 0) {
      // 优先选择未使用过的图片
      const unusedImages = formattedImages.filter(img => !usedImages.has(img.src))

      if (unusedImages.length === 0) {
        // 如果所有图片都用过了，重置已使用列表
        setUsedImages(new Set())
        availableImages = [...formattedImages]
      } else {
        availableImages = unusedImages
      }
    }

    // 处理图片数量不足的情况
    if (availableImages.length === 0) {
      return []
    }

    // 创建足够的图片副本以填满网格
    const extendedImages = [...availableImages]
    if (totalImages > availableImages.length) {
      // 需要重复图片来填满网格
      const repeatTimes = Math.ceil(totalImages / availableImages.length)
      for (let i = 1; i < repeatTimes; i++) {
        extendedImages.push(...availableImages)
      }
    }

    // 打乱图片顺序
    const shuffledImages = shuffleArray(extendedImages)
    const result: any[] = []
    const newUsedImages = new Set(usedImages)

    for (let row = 0; row < 3; row++) {
      const rowImages = new Set<string>() // 用于检查行内重复
      const rowStart = row * actualImagesPerRow

      for (let col = 0; col < actualImagesPerRow; col++) {
        let imageIndex = rowStart + col
        let attempts = 0
        const maxAttempts = shuffledImages.length * 2 // 动态调整最大尝试次数

        // 尝试找到一个不重复的图片
        while (attempts < maxAttempts) {
          const image = shuffledImages[imageIndex % shuffledImages.length]

          // 如果图片总数很少，允许行内重复
          const allowRowDuplication = totalAvailableImages < actualImagesPerRow

          if (!rowImages.has(image.src) || allowRowDuplication) {
            result.push(image)
            if (!allowRowDuplication) {
              rowImages.add(image.src)
            }
            if (ensureAllShown) {
              newUsedImages.add(image.src)
            }
            break
          }
          imageIndex++
          attempts++
        }

        // 如果实在找不到不重复的图片，就使用当前图片
        if (attempts >= maxAttempts) {
          const image = shuffledImages[imageIndex % shuffledImages.length]
          result.push(image)
          if (ensureAllShown) {
            newUsedImages.add(image.src)
          }
        }
      }
    }

    // 更新已使用的图片列表
    if (ensureAllShown) {
      setUsedImages(newUsedImages)
    }

    return result
  }, [animationConfig, usedImages])

  useEffect(() => {
    setItems(generateItems(animationConfig))
  }, [generateItems, animationConfig])

  // Set up automatic shuffling using config interval when not hovering
  useEffect(() => {
    if (isHovering) return

    const shuffleInterval = animationConfig?.shuffleInterval || 30000
    const interval = setInterval(() => {
      setItems(generateItems(animationConfig))
    }, shuffleInterval)

    return () => clearInterval(interval)
  }, [isHovering, generateItems, animationConfig])

  return (
    <div
      className={cn("w-full relative overflow-hidden pb-8", className)}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <GridMotion
        items={items}
        gradientColor="hsl(var(--brand))"
        isHovering={isHovering}
        animationConfig={animationConfig}
      />
    </div>
  )
}
