'use client';

import { useEffect, useState } from 'react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { drawModels } from '@/constants/draw/models';
import { Loader2 } from 'lucide-react';
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { useSimpleVisibilityPolling } from "@/lib/hooks/use-visibility-polling";

// 紧凑格式：[modelId, successRate]
type ModelStatusData = [string, number];

// 状态响应现在是数组格式，节省带宽
type StatusResponse = ModelStatusData[];

// 成功率颜色方案
const getSuccessRateColorScheme = (rate: number) => {
  if (rate >= 90) return {
    class: "bg-green-700", // 深绿
    barClass: "bg-gradient-to-r from-green-700 to-green-600"
  };
  if (rate >= 80) return {
    class: "bg-green-500", // 浅绿
    barClass: "bg-gradient-to-r from-green-500 to-green-400"
  };
  if (rate >= 70) return {
    class: "bg-yellow-500", // 黄色
    barClass: "bg-gradient-to-r from-yellow-500 to-yellow-400"
  };
  if (rate >= 60) return {
    class: "bg-red-400", // 浅红
    barClass: "bg-gradient-to-r from-red-400 to-red-300"
  };
  return {
    class: "bg-red-600", // 深红
    barClass: "bg-gradient-to-r from-red-600 to-red-500"
  };
};

export function ModelStatus() {
  const [statusData, setStatusData] = useState<StatusResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);

  // 获取状态数据的函数
  const fetchStatus = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/public/status');
      if (!response.ok) {
        throw new Error('Failed to fetch status');
      }
      const data: StatusResponse = await response.json();
      setStatusData(data);
      setLastUpdateTime(new Date());
    } catch (error) {
      console.error('Error fetching model status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 使用可见性感知的轮询
  const { startPolling, stopPolling, executeOnce } = useSimpleVisibilityPolling(
    fetchStatus,
    60 * 1000, // 60秒间隔
    {
      pauseOnHidden: true,     // 页面不可见时暂停
      executeOnVisible: true,  // 页面重新可见时立即执行
    }
  );

  // 手动刷新数据
  const handleManualRefresh = async () => {
    await executeOnce();
  };

  // 初始化和清理
  useEffect(() => {
    // 启动轮询（会立即执行一次）
    startPolling();

    // 组件卸载时停止轮询
    return () => {
      stopPolling();
    };
  }, [startPolling, stopPolling]);

  // 获取启用的模型列表
  const enabledModels = drawModels.filter(model => !model.disabled);

  // 渲染状态指示器
  const renderStatusIndicators = () => {
    if (isLoading || !statusData) {
      // 加载中或无数据时显示骨架屏
      return enabledModels.map((model, index) => (
        <Skeleton
          key={model.id}
          className="w-1 h-5 rounded-sm"
          style={{ opacity: 0.7 + (index * 0.05) }}
        />
      ));
    }

    // 将数组格式转换为Map以便快速查找
    const statusMap = new Map(statusData);

    // 有数据时根据成功率显示颜色
    return enabledModels.map(model => {
      const successRate = statusMap.get(model.id);
      if (successRate === undefined) return <Skeleton key={model.id} className="w-1 h-5 rounded-sm" />;

      // 获取颜色方案
      const colorScheme = getSuccessRateColorScheme(successRate);

      return successRate >= 0 ? (
        <div
          key={model.id}
          className={cn("w-1 h-5 rounded-sm", colorScheme.class)}
          title={`${model.name}: ${successRate}%`}
        />
      ) : (
        <Skeleton
          key={model.id}
          className="w-1 h-5 rounded-sm"
          title={`${model.name}: -`}
        />
      );
    });
  };

  // 渲染详细信息
  const renderDetailedStatus = () => {
    if (isLoading || !statusData) {
      return (
        <div className="p-2 space-y-2">
          <div className="flex items-center justify-center mb-2">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            <span>加载中...</span>
          </div>
          {enabledModels.map(model => (
            <div key={model.id} className="flex items-center gap-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-2 w-32" />
              <Skeleton className="h-4 w-16" />
            </div>
          ))}
        </div>
      );
    }

    // 将数组格式转换为Map以便快速查找
    const statusMap = new Map(statusData);

    return (
      <div className="p-2 space-y-2">
        {enabledModels.map(model => {
          const successRate = statusMap.get(model.id);
          if (successRate === undefined) return null;

          // 获取颜色方案
          const colorScheme = getSuccessRateColorScheme(successRate);

          return successRate >= 0 ? (
            <div key={model.id} className="flex items-center gap-2">
              <span
                className="text-sm font-medium truncate"
                title={model.name}
              >
                {model.name}
              </span>
              <div className="flex-1 min-w-32">
                <Progress
                  value={successRate}
                  colorScheme={colorScheme.barClass}
                  className="h-2"
                />
              </div>
              <span className="text-xs whitespace-nowrap w-8">
                {successRate}%
              </span>
            </div>
          ) : (
            <div key={model.id} className="flex items-center gap-2">
              <span
                className="text-sm font-medium truncate"
                title={model.name}
              >
                {model.name}
              </span>
              <Skeleton className="h-2 flex-1  min-w-32" />
              <span className="text-xs whitespace-nowrap w-8">-</span>
            </div>
          );
        })}
        <div className="flex items-center justify-between pt-1 border-t">
          <div className="text-xs text-muted-foreground">
            更新于: {lastUpdateTime?.toLocaleTimeString() || '未更新'}
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation(); // 防止事件冒泡
              handleManualRefresh();
            }}
            className="text-xs text-blue-500 hover:text-blue-700 transition-colors"
          >
            刷新
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="flex items-center gap-1 font-medium text-[0.8125rem] text-[#5E5F6E] hover:text-black transition-colors">
      {/* 点击文字部分刷新数据 */}
      <span
        className="cursor-pointer"
        onClick={handleManualRefresh}
        title="点击刷新状态数据"
      >
        系统状态:
      </span>

      {/* 点击竖线图标弹出详细列表 */}
      <Popover>
        <PopoverTrigger asChild>
          <div className="cursor-pointer">
            {isLoading ? (
              <Loader2 className="h-4 w-4 ml-1 animate-spin text-muted-foreground" />
            ) : (
              <div className="flex items-end gap-[2px] h-5">
                {renderStatusIndicators()}
              </div>
            )}
          </div>
        </PopoverTrigger>
        <PopoverContent side="top" align="center" className="w-120 p-0">
          {renderDetailedStatus()}
        </PopoverContent>
      </Popover>
    </div>
  );
}
