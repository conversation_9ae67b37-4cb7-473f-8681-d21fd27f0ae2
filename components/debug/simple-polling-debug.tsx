'use client';

import { useState, useEffect } from 'react';
import { useDrawStore } from '@/store/draw';

export function SimplePollingDebug() {
  const [debugInfo, setDebugInfo] = useState<{
    pageVisibility: string;
    timestamp: string;
    historyPollingState: any;
  }>({
    pageVisibility: 'visible',
    timestamp: new Date().toLocaleTimeString(),
    historyPollingState: null,
  });

  const { pollingManager, recentHistories, pendingHistories, isInitialHistoryLoad } = useDrawStore();

  // 监听页面可见性变化
  useEffect(() => {
    const handleVisibilityChange = () => {
      setDebugInfo(prev => ({
        ...prev,
        pageVisibility: document.visibilityState,
        timestamp: new Date().toLocaleTimeString(),
      }));
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  // 定期更新调试信息
  useEffect(() => {
    const interval = setInterval(() => {
      let historyPollingState = null;
      
      if (pollingManager) {
        try {
          const state = pollingManager.getState();
          historyPollingState = {
            isPolling: state.isPolling,
            currentInterval: state.currentInterval,
            consecutiveNoChanges: state.consecutiveNoChanges,
            isVisible: state.isVisible,
          };
        } catch (error) {
          console.error('Error getting polling manager state:', error);
        }
      }

      setDebugInfo(prev => ({
        ...prev,
        historyPollingState,
        timestamp: new Date().toLocaleTimeString(),
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, [pollingManager]);

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md text-sm z-50">
      <h3 className="font-bold mb-2">历史记录轮询状态</h3>
      
      <div className="space-y-2">
        <div>
          <strong>页面可见性:</strong> {debugInfo.pageVisibility}
        </div>
        
        <div>
          <strong>更新时间:</strong> {debugInfo.timestamp}
        </div>

        <div>
          <strong>轮询状态:</strong>
          {debugInfo.historyPollingState ? (
            <div className="ml-2 text-xs">
              <div>运行中: {debugInfo.historyPollingState.isPolling ? '✅ 是' : '❌ 否'}</div>
              <div>当前间隔: {debugInfo.historyPollingState.currentInterval}ms</div>
              <div>连续无变化: {debugInfo.historyPollingState.consecutiveNoChanges}次</div>
              <div>页面可见: {debugInfo.historyPollingState.isVisible ? '👁️ 是' : '🙈 否'}</div>
            </div>
          ) : (
            <span className="text-gray-500">未启动</span>
          )}
        </div>

        <div>
          <strong>历史记录数据:</strong>
          <div className="ml-2 text-xs">
            <div>最近记录: {recentHistories.length}</div>
            <div>待处理记录: {pendingHistories.length}</div>
            <div>初始加载: {isInitialHistoryLoad ? '是' : '否'}</div>
          </div>
        </div>

        <div className="pt-2 border-t text-xs text-gray-600">
          <div>💡 提示：打开控制台查看详细日志</div>
          <div>🔍 观察网络面板中的 /api/history/combined 请求</div>
        </div>
      </div>
    </div>
  );
}
