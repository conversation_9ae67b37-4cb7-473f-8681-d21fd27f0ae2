'use client';

import { useState, useEffect } from 'react';
import { useDrawStore } from '@/store/draw';
import { useVisibilityPolling } from '@/lib/hooks/use-visibility-polling';

export function PollingDebug() {
  const [debugInfo, setDebugInfo] = useState<{
    historyPolling: any;
    testPolling: any;
    pageVisibility: string;
    timestamp: string;
  }>({
    historyPolling: null,
    testPolling: null,
    pageVisibility: 'visible',
    timestamp: new Date().toLocaleTimeString(),
  });

  const { pollingManager, recentHistories, pendingHistories, isInitialHistoryLoad } = useDrawStore();

  // 测试轮询
  const [testCounter, setTestCounter] = useState(0);
  const testPollingCallback = async () => {
    setTestCounter(prev => prev + 1);
    console.log('Test polling executed:', testCounter + 1);
    return Math.random() > 0.7; // 30% 概率返回有变化
  };

  const testPolling = useVisibilityPolling(testPollingCallback, {
    initialInterval: 3000,
    maxInterval: 15000,
    intervalStep: 1000,
    enableAdaptiveInterval: true,
    pauseOnHidden: true,
    executeOnVisible: true,
  });

  // 监听页面可见性变化
  useEffect(() => {
    const handleVisibilityChange = () => {
      setDebugInfo(prev => ({
        ...prev,
        pageVisibility: document.visibilityState,
        timestamp: new Date().toLocaleTimeString(),
      }));
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  // 更新调试信息
  useEffect(() => {
    const interval = setInterval(() => {
      setDebugInfo(prev => ({
        ...prev,
        historyPolling: pollingManager ? (() => {
          try {
            const state = pollingManager.getState();
            return {
              isPolling: state.isPolling,
              currentInterval: state.currentInterval,
              consecutiveNoChanges: state.consecutiveNoChanges,
              isVisible: state.isVisible,
            };
          } catch (error) {
            console.error('Error getting polling manager state:', error);
            return null;
          }
        })() : null,
        testPolling: testPolling ? {
          isPolling: testPolling.isPolling,
          currentInterval: testPolling.currentInterval,
          consecutiveNoChanges: testPolling.consecutiveNoChanges,
        } : null,
        timestamp: new Date().toLocaleTimeString(),
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, []); // 移除依赖，避免无限重新创建

  // 启动测试轮询
  useEffect(() => {
    if (testPolling) {
      const stopFunction = testPolling.startPolling();
      return stopFunction;
    }
  }, [testPolling]);

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md text-sm">
      <h3 className="font-bold mb-2">轮询调试信息</h3>

      <div className="space-y-2">
        <div>
          <strong>页面可见性:</strong> {debugInfo.pageVisibility}
        </div>

        <div>
          <strong>更新时间:</strong> {debugInfo.timestamp}
        </div>

        <div>
          <strong>历史记录轮询:</strong>
          {debugInfo.historyPolling ? (
            <div className="ml-2 text-xs">
              <div>运行中: {debugInfo.historyPolling.isPolling ? '是' : '否'}</div>
              <div>当前间隔: {debugInfo.historyPolling.currentInterval}ms</div>
              <div>连续无变化: {debugInfo.historyPolling.consecutiveNoChanges}</div>
              <div>页面可见: {debugInfo.historyPolling.isVisible ? '是' : '否'}</div>
            </div>
          ) : (
            <span className="text-gray-500">未启动</span>
          )}
        </div>

        <div>
          <strong>测试轮询:</strong>
          {debugInfo.testPolling ? (
            <div className="ml-2 text-xs">
              <div>运行中: {debugInfo.testPolling.isPolling ? '是' : '否'}</div>
              <div>当前间隔: {debugInfo.testPolling.currentInterval}ms</div>
              <div>连续无变化: {debugInfo.testPolling.consecutiveNoChanges}</div>
              <div>执行次数: {testCounter}</div>
            </div>
          ) : (
            <span className="text-gray-500">未启动</span>
          )}
        </div>

        <div>
          <strong>历史记录数据:</strong>
          <div className="ml-2 text-xs">
            <div>最近记录: {recentHistories.length}</div>
            <div>待处理记录: {pendingHistories.length}</div>
            <div>初始加载: {isInitialHistoryLoad ? '是' : '否'}</div>
          </div>
        </div>

        <div className="flex gap-2 mt-3">
          <button
            onClick={() => testPolling?.executeOnce()}
            disabled={!testPolling}
            className="px-2 py-1 bg-blue-500 text-white rounded text-xs disabled:bg-gray-400"
          >
            立即执行测试
          </button>
          <button
            onClick={() => testPolling?.resetInterval()}
            disabled={!testPolling}
            className="px-2 py-1 bg-green-500 text-white rounded text-xs disabled:bg-gray-400"
          >
            重置间隔
          </button>
        </div>
      </div>
    </div>
  );
}
