'use client';

import { useEffect } from 'react';
import { useDrawStore } from '@/store/draw';
import { SimplePollingDebug } from '@/components/debug/simple-polling-debug';

export default function TestPollingPage() {
  const { startHistoryPolling, stopHistoryPolling } = useDrawStore();

  useEffect(() => {
    // 启动历史记录轮询
    const stopFunction = startHistoryPolling();

    return () => {
      if (stopFunction) {
        stopFunction();
      }
    };
  }, []); // 移除依赖，只在组件挂载时执行一次

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">轮询优化测试页面</h1>

      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">测试说明</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>页面加载后会自动启动历史记录轮询</li>
            <li>初始间隔为5秒，如果没有数据变化，间隔会逐渐增加到最大30秒</li>
            <li>切换到其他标签页时，轮询会暂停</li>
            <li>重新回到页面时，会立即执行一次查询并恢复轮询</li>
            <li>右下角的调试面板显示实时的轮询状态</li>
          </ul>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">验证步骤</h2>
          <ol className="list-decimal list-inside space-y-1 text-sm">
            <li>打开浏览器开发者工具的网络面板</li>
            <li>观察 <code>/api/history/combined</code> 请求的频率</li>
            <li>初始几次请求应该是5秒间隔</li>
            <li>如果没有数据变化，间隔应该逐渐增加：7秒、9秒、11秒...最大30秒</li>
            <li>切换到其他标签页，请求应该停止</li>
            <li>切换回来时，应该立即发送一次请求</li>
          </ol>
        </div>

        <div className="bg-green-50 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">预期行为</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li><strong>初始加载</strong>: 不会重置轮询间隔（避免一直保持5秒间隔）</li>
            <li><strong>有数据变化</strong>: 重置间隔为5秒</li>
            <li><strong>无数据变化</strong>: 间隔逐渐增加</li>
            <li><strong>页面隐藏</strong>: 暂停所有轮询</li>
            <li><strong>页面显示</strong>: 立即查询并恢复轮询</li>
          </ul>
        </div>
      </div>

      {/* 调试面板 */}
      <SimplePollingDebug />
    </div>
  );
}
