/**
 * 页面可见性感知的轮询管理器
 * 用于在 Zustand store 或其他非 React 环境中使用
 */

interface PollingOptions {
  /** 初始轮询间隔（毫秒） */
  initialInterval?: number;
  /** 最大轮询间隔（毫秒） */
  maxInterval?: number;
  /** 间隔增长步长（毫秒） */
  intervalStep?: number;
  /** 是否启用智能频率调整 */
  enableAdaptiveInterval?: boolean;
  /** 页面不可见时是否停止轮询 */
  pauseOnHidden?: boolean;
  /** 页面重新可见时是否立即执行一次 */
  executeOnVisible?: boolean;
}

interface PollingState {
  isPolling: boolean;
  currentInterval: number;
  consecutiveNoChanges: number;
  isVisible: boolean;
}

export class VisibilityPollingManager {
  private timeout: NodeJS.Timeout | null = null;
  private state: PollingState;
  private callback: () => Promise<boolean>;
  private options: Required<PollingOptions>;
  private visibilityHandler: () => void;

  constructor(
    callback: () => Promise<boolean>,
    options: PollingOptions = {}
  ) {
    this.callback = callback;
    this.options = {
      initialInterval: 5000,
      maxInterval: 30000,
      intervalStep: 2000,
      enableAdaptiveInterval: true,
      pauseOnHidden: true,
      executeOnVisible: true,
      ...options,
    };

    this.state = {
      isPolling: false,
      currentInterval: this.options.initialInterval,
      consecutiveNoChanges: 0,
      isVisible: typeof document !== 'undefined' ? document.visibilityState === 'visible' : true,
    };

    // 绑定页面可见性变化处理器
    this.visibilityHandler = this.handleVisibilityChange.bind(this);
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', this.visibilityHandler);
    }
  }

  private async handleVisibilityChange() {
    const wasVisible = this.state.isVisible;
    this.state.isVisible = document.visibilityState === 'visible';

    if (this.state.isVisible && !wasVisible) {
      // 页面从不可见变为可见
      console.log('Page became visible, resuming polling');

      if (this.options.executeOnVisible) {
        await this.executeCallback();
      }

      // 如果轮询已启动但被暂停，现在恢复
      if (this.state.isPolling && !this.timeout) {
        this.scheduleNext();
      }
    } else if (!this.state.isVisible && wasVisible) {
      console.log('Page became hidden, pausing polling');
    }
  }

  private async executeCallback(): Promise<boolean> {
    try {
      const hasChanges = await this.callback();

      if (this.options.enableAdaptiveInterval) {
        if (hasChanges) {
          // 有变化时重置间隔
          this.state.consecutiveNoChanges = 0;
          this.state.currentInterval = this.options.initialInterval;
          console.log('🔄 Data changed, reset polling interval to', this.state.currentInterval, 'ms');
        } else {
          // 无变化时增加间隔
          this.state.consecutiveNoChanges++;
          const newInterval = Math.min(
            this.options.initialInterval + this.state.consecutiveNoChanges * this.options.intervalStep,
            this.options.maxInterval
          );
          if (newInterval !== this.state.currentInterval) {
            this.state.currentInterval = newInterval;
            console.log('⏰ No data changes (' + this.state.consecutiveNoChanges + ' times), increased polling interval to', this.state.currentInterval, 'ms');
          } else {
            console.log('📊 No data changes, keeping interval at', this.state.currentInterval, 'ms');
          }
        }
      }

      return hasChanges;
    } catch (error) {
      console.error('Polling callback error:', error);
      return false;
    }
  }

  private scheduleNext() {
    if (!this.state.isPolling) return;

    console.log('⏱️ Scheduling next poll in', this.state.currentInterval, 'ms');
    this.timeout = setTimeout(async () => {
      // 检查页面可见性
      if (this.options.pauseOnHidden && !this.state.isVisible) {
        // 页面不可见时，延迟重新检查
        console.log('👁️ Page not visible, delaying poll');
        this.timeout = setTimeout(() => this.scheduleNext(), 1000);
        return;
      }

      console.log('🔍 Executing scheduled poll');
      await this.executeCallback();
      this.scheduleNext();
    }, this.state.currentInterval);
  }

  /**
   * 启动轮询
   */
  start(): () => void {
    if (this.state.isPolling) {
      console.warn('Polling is already running');
      return this.stop.bind(this);
    }

    console.log('Starting visibility-aware polling');
    this.state.isPolling = true;

    // 立即执行一次
    this.executeCallback().then(() => {
      this.scheduleNext();
    });

    // 返回停止函数
    return this.stop.bind(this);
  }

  /**
   * 停止轮询
   */
  stop() {
    if (!this.state.isPolling) return;

    console.log('Stopping visibility-aware polling');
    this.state.isPolling = false;

    if (this.timeout) {
      clearTimeout(this.timeout);
      this.timeout = null;
    }
  }

  /**
   * 立即执行一次回调（不影响轮询计划）
   */
  async executeOnce(): Promise<boolean> {
    return await this.executeCallback();
  }

  /**
   * 重置轮询间隔
   */
  resetInterval() {
    this.state.currentInterval = this.options.initialInterval;
    this.state.consecutiveNoChanges = 0;
    console.log('Reset polling interval to', this.state.currentInterval);
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      isPolling: this.state.isPolling,
      currentInterval: this.state.currentInterval,
      consecutiveNoChanges: this.state.consecutiveNoChanges,
      isVisible: this.state.isVisible,
    };
  }

  /**
   * 销毁管理器，清理资源
   */
  destroy() {
    this.stop();
    if (typeof document !== 'undefined') {
      document.removeEventListener('visibilitychange', this.visibilityHandler);
    }
  }
}

/**
 * 创建一个简化的轮询管理器，只处理页面可见性
 */
export function createSimpleVisibilityPolling(
  callback: () => Promise<void>,
  interval: number = 5000,
  options: { pauseOnHidden?: boolean; executeOnVisible?: boolean } = {}
) {
  const { pauseOnHidden = true, executeOnVisible = true } = options;

  return new VisibilityPollingManager(
    async () => {
      await callback();
      return false; // 不启用智能频率调整
    },
    {
      initialInterval: interval,
      enableAdaptiveInterval: false,
      pauseOnHidden,
      executeOnVisible,
    }
  );
}
