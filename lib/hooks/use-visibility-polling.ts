import { useEffect, useRef, useCallback } from 'react';

interface PollingOptions {
  /** 初始轮询间隔（毫秒） */
  initialInterval?: number;
  /** 最大轮询间隔（毫秒） */
  maxInterval?: number;
  /** 间隔增长步长（毫秒） */
  intervalStep?: number;
  /** 是否启用智能频率调整 */
  enableAdaptiveInterval?: boolean;
  /** 页面不可见时是否停止轮询 */
  pauseOnHidden?: boolean;
  /** 页面重新可见时是否立即执行一次 */
  executeOnVisible?: boolean;
}

interface PollingState {
  isPolling: boolean;
  currentInterval: number;
  consecutiveNoChanges: number;
}

/**
 * 页面可见性感知的轮询 Hook
 * 支持智能频率调整和页面可见性控制
 */
export function useVisibilityPolling(
  callback: () => Promise<boolean> | boolean,
  options: PollingOptions = {}
) {
  const {
    initialInterval = 5000,
    maxInterval = 30000,
    intervalStep = 2000,
    enableAdaptiveInterval = true,
    pauseOnHidden = true,
    executeOnVisible = true,
  } = options;

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const stateRef = useRef<PollingState>({
    isPolling: false,
    currentInterval: initialInterval,
    consecutiveNoChanges: 0,
  });

  const isVisibleRef = useRef(true);

  // 清除定时器
  const clearPolling = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    stateRef.current.isPolling = false;
  }, []);

  // 执行轮询回调并处理结果
  const executeCallback = useCallback(async () => {
    try {
      const hasChanges = await callback();

      if (enableAdaptiveInterval) {
        if (hasChanges) {
          // 有变化时重置间隔
          stateRef.current.consecutiveNoChanges = 0;
          stateRef.current.currentInterval = initialInterval;
        } else {
          // 无变化时增加间隔
          stateRef.current.consecutiveNoChanges++;
          const newInterval = Math.min(
            initialInterval + stateRef.current.consecutiveNoChanges * intervalStep,
            maxInterval
          );
          stateRef.current.currentInterval = newInterval;
        }
      }

      return hasChanges;
    } catch (error) {
      console.error('Polling callback error:', error);
      return false;
    }
  }, [callback, enableAdaptiveInterval, initialInterval, intervalStep, maxInterval]);

  // 停止轮询
  const stopPolling = useCallback(() => {
    clearPolling();
  }, [clearPolling]);

  // 启动轮询
  const startPolling = useCallback(() => {
    if (stateRef.current.isPolling) return stopPolling;

    const poll = async () => {
      // 检查页面可见性
      if (pauseOnHidden && !isVisibleRef.current) {
        // 页面不可见时，延迟重新检查
        timeoutRef.current = setTimeout(poll, 1000);
        return;
      }

      await executeCallback();

      // 继续下一次轮询
      if (stateRef.current.isPolling) {
        timeoutRef.current = setTimeout(poll, stateRef.current.currentInterval);
      }
    };

    stateRef.current.isPolling = true;
    poll();

    // 返回停止函数
    return stopPolling;
  }, [executeCallback, pauseOnHidden, stopPolling]);

  // 立即执行一次（不影响轮询计划）
  const executeOnce = useCallback(async () => {
    return await executeCallback();
  }, [executeCallback]);

  // 重置轮询间隔
  const resetInterval = useCallback(() => {
    stateRef.current.currentInterval = initialInterval;
    stateRef.current.consecutiveNoChanges = 0;
  }, [initialInterval]);

  // 处理页面可见性变化
  useEffect(() => {
    if (!pauseOnHidden) return;

    const handleVisibilityChange = async () => {
      const isVisible = document.visibilityState === 'visible';
      const wasVisible = isVisibleRef.current;
      isVisibleRef.current = isVisible;

      if (isVisible && !wasVisible) {
        // 页面从不可见变为可见
        if (executeOnVisible) {
          await executeCallback();
        }
        // 如果轮询已启动但被暂停，现在恢复
        if (stateRef.current.isPolling && !timeoutRef.current) {
          startPolling();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [executeCallback, executeOnVisible, pauseOnHidden, startPolling]);

  // 清理定时器
  useEffect(() => {
    return () => {
      clearPolling();
    };
  }, [clearPolling]);

  return {
    startPolling,
    stopPolling,
    executeOnce,
    resetInterval,
    isPolling: stateRef.current.isPolling,
    currentInterval: stateRef.current.currentInterval,
    consecutiveNoChanges: stateRef.current.consecutiveNoChanges,
  };
}

/**
 * 简化版本的可见性轮询 Hook
 * 只处理页面可见性，不包含智能频率调整
 */
export function useSimpleVisibilityPolling(
  callback: () => Promise<void> | void,
  interval: number = 5000,
  options: { pauseOnHidden?: boolean; executeOnVisible?: boolean } = {}
) {
  const { pauseOnHidden = true, executeOnVisible = true } = options;

  return useVisibilityPolling(
    async () => {
      await callback();
      return false; // 不启用智能频率调整
    },
    {
      initialInterval: interval,
      enableAdaptiveInterval: false,
      pauseOnHidden,
      executeOnVisible,
    }
  );
}
