import { useState, useEffect, useCallback } from 'react';
import { useVisibilityPolling } from './use-visibility-polling';

export type OrderStatus = 'PENDING' | 'SUCCESS' | 'FAILED' | 'REFUND';

interface OrderStatusResponse {
    status: OrderStatus;
    orderId: string;
    amount: number;
    createdAt: string;
}

interface UseOrderPollingProps {
    orderId: string | null;
    onSuccess?: () => Promise<void> | void;
    onFailed?: () => void;
}

interface OrderPollingState {
    status: OrderStatus | null;
    error: string | null;
    isPolling: boolean;
}

export function useOrderPolling({ orderId, onSuccess, onFailed }: UseOrderPollingProps) {
    const [state, setState] = useState<OrderPollingState>({
        status: null,
        error: null,
        isPolling: false
    });

    // 轮询回调函数
    const pollOrder = useCallback(async (): Promise<boolean> => {
        if (!orderId) return true; // 停止轮询

        try {
            const response = await fetch(`/api/orders/${orderId}/status`);
            if (!response.ok) {
                throw new Error("查询订单状态失败");
            }

            const result = await response.json();
            if (result.code !== 0) {
                throw new Error(result.msg || "查询订单状态失败");
            }

            const data: OrderStatusResponse = result.data;
            setState(prev => ({ ...prev, status: data.status, error: null }));

            if (data.status === "SUCCESS") {
                await onSuccess?.();
                return true; // 停止轮询，返回 true 表示有变化
            }

            if (data.status === "FAILED" || data.status === "REFUND") {
                onFailed?.();
                return true; // 停止轮询，返回 true 表示有变化
            }

            return false; // 继续轮询，返回 false 表示无关键变化
        } catch (error) {
            setState(prev => ({
                ...prev,
                error: error instanceof Error ? error.message : "查询订单状态失败"
            }));
            return true; // 停止轮询，返回 true 表示有错误变化
        }
    }, [orderId, onSuccess, onFailed]);

    // 使用可见性感知的轮询
    const { startPolling, stopPolling } = useVisibilityPolling(
        pollOrder,
        {
            initialInterval: 2000,      // 初始2秒间隔（订单状态需要较快响应）
            maxInterval: 10000,         // 最大10秒间隔
            intervalStep: 1000,         // 每次增加1秒
            enableAdaptiveInterval: false, // 订单轮询不使用自适应间隔，保持快速响应
            pauseOnHidden: true,        // 页面不可见时暂停
            executeOnVisible: true,     // 页面重新可见时立即执行
        }
    );

    useEffect(() => {
        if (!orderId) {
            setState({
                status: null,
                error: null,
                isPolling: false
            });
            return;
        }

        setState(prev => ({ ...prev, isPolling: true }));

        // 启动轮询
        const stopFunction = startPolling();

        // 清理函数
        return () => {
            setState(prev => ({ ...prev, isPolling: false }));
            stopFunction();
        };
    }, [orderId, startPolling]);

    return state;
}
