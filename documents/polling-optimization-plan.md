# 轮询优化方案

## 目标
通过页面可见性优化和智能轮询频率调整，降低数据库查询频次，减少服务器负载。

## 当前轮询状态分析

### 1. 历史记录轮询 (`/api/history/combined`)
- **位置**: `store/draw/index.ts`
- **当前频率**: 每5秒轮询一次
- **问题**: 无论页面是否可见都持续轮询，造成不必要的数据库查询

### 2. 模型状态轮询 (`/api/public/status`)
- **位置**: `components/global/model-status.tsx`
- **当前频率**: 每60秒轮询一次
- **问题**: 页面不可见时仍在轮询

### 3. 订单状态轮询
- **位置**: `lib/hooks/use-order-polling.ts`
- **当前频率**: 每2秒轮询一次
- **问题**: 高频轮询，需要优化

## 优化策略

### 阶段一：页面可见性优化
**目标**: 页面不可见时停止轮询，可见时恢复轮询
**预期效果**: 减少50-70%的后台查询

#### 1.1 历史记录轮询优化
- 监听 `visibilitychange` 事件
- 页面不可见时清除定时器
- 页面重新可见时立即查询一次并恢复轮询

#### 1.2 模型状态轮询优化
- 同样实现页面可见性控制
- 页面重新可见时立即刷新状态

#### 1.3 订单状态轮询优化
- 仅在页面可见且订单为待处理状态时轮询
- 页面不可见时暂停轮询

### 阶段二：智能轮询频率调整
**目标**: 根据数据变化动态调整轮询频率
**预期效果**: 再减少30-50%的查询次数

#### 2.1 自适应轮询间隔
- 初始间隔：5秒
- 连续无变化时逐渐增加间隔：5s → 10s → 15s → 20s → 30s（最大）
- 有数据变化时重置为5秒

#### 2.2 状态感知轮询
- 有待处理任务时：高频轮询（5秒）
- 无待处理任务时：低频轮询（30秒）
- 用户活跃时：正常频率
- 用户不活跃时：降低频率

## 实施计划

### 第一步：创建通用的页面可见性管理器
创建 `lib/hooks/use-visibility-polling.ts`，提供统一的页面可见性轮询控制。

### 第二步：优化历史记录轮询
修改 `store/draw/index.ts` 中的 `startHistoryPolling` 方法。

### 第三步：优化模型状态轮询
修改 `components/global/model-status.tsx` 中的轮询逻辑。

### 第四步：优化订单状态轮询
修改 `lib/hooks/use-order-polling.ts` 中的轮询逻辑。

### 第五步：实现智能轮询频率调整
为各个轮询添加自适应间隔逻辑。

## 预期效果

### 数据库查询减少量估算
- **页面可见性优化**: 减少50-70%的后台查询
- **智能频率调整**: 再减少30-50%的查询
- **总体预期**: 减少65-85%的不必要查询

### 用户体验改善
- 页面重新可见时立即获取最新数据
- 有数据变化时保持响应速度
- 无变化时减少资源消耗

## 监控指标

### 实施前后对比
- 每分钟数据库查询次数
- 服务器CPU和内存使用率
- 用户端网络请求数量
- 页面响应时间

### 关键监控点
- 轮询间隔调整的触发频率
- 页面可见性变化的处理效果
- 用户活跃度对轮询频率的影响

## 风险评估

### 潜在风险
1. 页面长时间不可见后，重新可见时数据可能较多
2. 智能调整可能导致某些情况下响应延迟

### 风险缓解
1. 页面重新可见时立即查询最新数据
2. 保留手动刷新功能作为备选
3. 对关键状态（如支付）保持较高轮询频率

## 后续优化方向

1. **用户行为分析**: 根据用户操作模式进一步优化轮询策略
2. **缓存策略**: 在客户端实现智能缓存，减少重复查询
3. **批量查询**: 将多个小查询合并为批量查询
4. **条件查询**: 只查询有变化的数据项

## 实施进度

### ✅ 已完成

#### 第一步：创建通用的页面可见性管理器
- ✅ 创建了 `lib/hooks/use-visibility-polling.ts` - React Hook 版本
- ✅ 创建了 `lib/polling/visibility-polling-manager.ts` - 独立管理器版本
- ✅ 支持页面可见性控制和智能频率调整

#### 第二步：优化历史记录轮询
- ✅ 修改 `store/draw/index.ts` 中的轮询逻辑
- ✅ 使用 `VisibilityPollingManager` 替换原有的 `setInterval`
- ✅ 实现智能频率调整：5s → 30s（最大）
- ✅ 页面不可见时自动暂停轮询
- ✅ 页面重新可见时立即获取数据

#### 第三步：优化模型状态轮询
- ✅ 修改 `components/global/model-status.tsx`
- ✅ 使用 `useSimpleVisibilityPolling` Hook
- ✅ 60秒间隔轮询，页面不可见时暂停
- ✅ 显示准确的最后更新时间

#### 第四步：优化订单状态轮询
- ✅ 修改 `lib/hooks/use-order-polling.ts`
- ✅ 使用 `useVisibilityPolling` Hook
- ✅ 2秒间隔轮询，页面不可见时暂停
- ✅ 保持快速响应，不使用自适应间隔

#### 第五步：创建调试工具
- ✅ 创建 `components/debug/polling-debug.tsx` 用于监控轮询状态

### 🔄 配置详情

#### 历史记录轮询配置
- 初始间隔：5秒
- 最大间隔：30秒
- 间隔增长：每次无变化增加2秒
- 智能调整：启用
- 页面可见性控制：启用

#### 模型状态轮询配置
- 固定间隔：60秒
- 智能调整：禁用
- 页面可见性控制：启用

#### 订单状态轮询配置
- 固定间隔：2秒
- 智能调整：禁用（保持快速响应）
- 页面可见性控制：启用

## 实施时间表

- ✅ **第1-2天**: 创建通用工具和优化历史记录轮询
- ✅ **第3天**: 优化模型状态和订单状态轮询
- ✅ **第4-5天**: 实现智能频率调整
- 🔄 **第6天**: 测试和调优
- 📋 **第7天**: 监控和文档更新
